

# 宠物医疗大模型评测数据集管理平台开发提示词

## 项目概述
您是一位资深的产品经理和技术架构师，需要设计并开发一个专业的**宠物医疗垂直领域大模型评测数据集管理平台**。该平台旨在为宠物医疗AI应用提供标准化、多维度的数据集管理和质量控制服务。

## 系统功能架构

### 1. 评测数据集管理
**核心目标**：提供完整的数据集生命周期管理

**主要功能模块**：
- **数据集CRUD操作**：新增、编辑、导入、查询、查看、删除
- **批量数据处理**：支持Excel文件导入，包含图片附件处理
- **数据质量控制**：字段验证、格式检查、完整性校验
- **权限控制**：基于角色的数据访问和操作权限

### 2. 评测数据集审核
**核心目标**：通过专业的审核，确保数据集的专业性和准确性

**审核流程设计**：
- **初审**：系统自动验证（格式、完整性、规范性）
- **专家审核**：领域专家人工审核（专业性、准确性）
- **状态管理**：待审核、审核中、已通过、已拒绝、需修改

**审核标准**：
- **数据完整性**：必填字段完整，格式规范
- **专业准确性**：医学术语准确，诊断合理
- **图片质量**：清晰度、标注准确性
- **伦理合规**：隐私保护、数据脱敏

### 3. 后台权限与配置管理
**用户角色设计**：
- **系统管理员**：全局配置、用户管理、系统监控
- **数据管理员**：数据集管理、导入导出、质量控制
- **审核专家**：专业审核、质量评估、标准制定
- **普通用户**：数据录入、查看自己创建的数据

**权限控制矩阵**：
- **数据级权限**：只能访问自己创建或被授权的数据
- **功能级权限**：根据角色限制可执行的操作
- **审核权限**：专家只能审核自己专业领域的数据

## 界面设计规范

### 主界面设计
**布局结构**：
- **顶部导航栏**：平台Logo、主要功能模块、用户信息
- **主内容区**：数据集概览卡片、统计信息仪表盘
- **底部状态栏**：系统状态、版本信息、帮助链接

**数据集概览卡片**：
```
┌─────────────────────────────────┐
│ 综合性病例数据集                │
│ 📊 数据量：1,234 条             │
│ ✅ 已审核：1,100 条             │
│ ⏳ 待审核：134 条               │
│ 👤 贡献者：45 人                │
│ [进入管理] [查看统计]           │
└─────────────────────────────────┘
```

### 数据集管理界面设计
**数据集管理工作台布局**：
- **筛选区域**（顶部）：多条件组合筛选、高级搜索
- **操作区域**（右上角）：新增、批量导入、导出、设置
- **列表区域**（主体）：数据列表、分页、排序
- **详情区域**（右侧或弹窗）：数据详情查看、编辑表单

**筛选功能设计**：
- **综合性病例数据集筛选字段**：
  - 病例标签、专科分类、科室/疾病类型
  - 品类、品种、性别

- **影像数据集筛选字段**：
  - 系统类型、分类、详细分类

- **执兽医考试数据集筛选字段**：
  - 题型

### 审核界面设计
**审核工作台布局**：
- **顶部筛选区域**：针对该数据集的查询过滤下拉列表框
- **顶部操作区域**（右侧）：查询按钮、批量审核按钮
- **主体列表区域**：默认显示待审核数据，支持单条选择和批量选择
- **详情查看区域**：弹窗式数据详情查看界面

**筛选功能设计**：
- **综合性病例数据集筛选字段**：
  - 病例标签、专科分类、科室/疾病类型
  - 品类、品种、性别、创建人

- **影像数据集筛选字段**：
  - 系统类型、分类、详细分类、创建人

- **执兽医考试数据集筛选字段**：
  - 题型、创建人

**数据列表设计**：
- **默认显示**：仅显示状态为"待审核"的数据
- **列表左侧**：复选框，支持单选和全选
- **列表主体**：显示数据的关键信息字段
- **列表右侧**：每行数据的操作按钮（审核、查看）

**操作功能**：
- **查询操作**：根据筛选条件查询待审核数据
- **批量审核**：选中多条数据进行批量审核操作
- **单条审核**：点击单行的审核按钮进行审核
- **数据查看**：点击查看按钮弹出表单式详情界面

**审核操作流程**：
- **单条审核**：点击审核按钮 → 弹出审核表单 → 选择通过/拒绝 → 填写审核意见 → 提交
- **批量审核**：选择多条数据 → 点击批量审核 → 选择批量操作类型 → 填写审核意见 → 确认提交

## 数据集标准化定义

### 综合性病例数据集
**基础信息字段**：
- **病例ID**：系统自动生成的唯一标识
- **创建时间**：数据录入时间戳
- **最后修改时间**：数据最后更新时间
- **审核状态**：待审核、已通过、已拒绝、需修改

**分类标签字段**：
- **病例标签**：多选下拉，支持自定义标签
- **专科分类**：基于兽医专科体系的标准分类
- **科室/疾病类型**：参考《兽医科室分类标准》

**动物信息字段**：
- **品类**：犬、猫、兔、鸟类等（单选）
- **品种**：具体品种信息（单选，级联品类）
- **性别**：雄性、雌性、未知（单选）
- **年龄**：数值+单位（月/岁），支持范围输入
- **体重**：数值（kg），支持小数点后一位
- **绝育状态**：已绝育、未绝育、未知（单选）

**临床信息字段**：
- **主诉/症状**：富文本编辑器，支持医学术语高亮
- **既往病史**：结构化病史记录，支持时间轴展示
- **体格检查**：按系统分类的检查结果

**化验与影像字段**：
- **实验室检查**：结构化检验报告，
- **化验单图片**：支持多文件上传，自动压缩和格式转换
- **影像检查**：影像报告
- **影像图片**：jpeg、png图片
- **其他图片**：临床照片、病理图片等

**诊疗信息字段**：
- **诊断结果**：主要诊断+次要诊断，支持ICD编码
- **治疗方案**：结构化治疗计划，包含用药、手术、护理
- **用药信息**：药品名称、剂量、频次、疗程
- **预后评估**：治疗效果评估，支持评分量表
- **医嘱**: 出院医嘱、随访建议等

### 影像数据集
**字段定义**:
- 疾病系统分类：呼吸系统、消化系统、泌尿系统等
- 部位分类：头颈部、胸部、腹部、四肢等
- 详细部分：下拉单选
- 检查类型：X光、CT、MRI、超声等
- 病变类型：正常、炎症、肿瘤、外伤等
- 征象：部位以及文字描述
- 影像图片：可以上传多个

### 执兽医考试数据集
**题目分类体系**：
- **学科分类**：基础兽医学、预防兽医学、临床兽医学
- **难度等级**：基础、中级、高级、专家级
- **题型分类**：单选题、多选题、判断题、案例分析题
- **知识点标签**：细粒度的知识点分类和标签

**字段定义**:
- 题型：单选、多选、判断题
- 题目：文字
- 选项A：文字
- 选项B：文字
- 选项C：文字
- 选项D：文字
- 选项E：文字
- 答案：文字
- 答案解析：文字

## 技术实现要求

### 系统架构设计
- **前端技术栈**：Vue.js 3 + TypeScript + Element Plus
- **后端技术栈**：Spring Boot + MyBatis Plus + Redis
- **数据库设计**：MySQL 8.0 + MongoDB（存储非结构化数据）
- **文件存储**：阿里云OSS + CDN加速
- **搜索引擎**：Elasticsearch（支持全文搜索和复杂查询）

### 性能优化要求
- **响应时间**：页面加载≤2秒，API响应≤500ms
- **并发处理**：支持1000+并发用户
- **文件上传**：支持断点续传、秒传、批量上传
- **数据缓存**：Redis缓存热点数据，提升查询性能

### 安全性要求
- **数据加密**：敏感数据AES-256加密存储
- **访问控制**：JWT Token + RBAC权限模型
- **审计日志**：完整记录用户操作和数据变更
- **数据备份**：每日自动备份，异地容灾

## 质量控制体系

### 数据质量标准
- **完整性检查**：必填字段完整率≥95%
- **准确性验证**：专家审核通过率≥90%
- **一致性保证**：同类数据格式统一，术语规范
- **时效性要求**：数据录入后24小时内完成初审

### 审核流程标准
- **自动预审**：系统自动检查格式和完整性
- **专家审核**：领域专家审核专业性和准确性，直接决定通过或拒绝
- **反馈机制**：审核意见及时反馈，支持修改重审
- **一次性决策**：专家审核后直接确定最终状态，提高审核效率

### 持续改进机制
- **用户反馈**：收集用户使用反馈，持续优化
- **专家建议**：定期召开专家委员会，完善标准
- **技术升级**：跟踪前沿技术，持续技术创新
- **标准更新**：根据行业发展，及时更新标准

## 项目实施计划

### 第一阶段：基础平台搭建（1-2个月）
- 系统架构设计和技术选型
- 基础功能模块开发（用户管理、权限控制）
- 数据库设计和基础数据导入
- 基础界面框架和组件库

### 第二阶段：核心功能开发（2-3个月）
- 数据集管理功能完整实现
- 审核流程和工作台开发
- 文件上传和多媒体处理
- 搜索和筛选功能优化

### 第三阶段：高级功能和优化（1-2个月）
- 数据可视化和统计分析
- 批量操作和自动化工具
- 性能优化和安全加固
- 移动端适配和响应式设计

### 第四阶段：测试和上线（1个月）
- 全面功能测试和性能测试
- 用户培训和文档编写
- 生产环境部署和监控
- 正式上线和运营支持

请基于以上优化后的提示词，开始设计和开发这个专业的宠物医疗大模型评测数据集管理平台。
